<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <div class="search-form">
                <el-form :model="searchForm" inline label-width="10px">
                    <el-form-item label="">
                        <el-input
                            v-model="searchForm.resourceName"
                            placeholder="请输入资源名称"
                            clearable
                            class="w200"
                        />
                    </el-form-item>

                    <el-form-item label="">
                        <el-select
                            v-model="searchForm.resourceType"
                            placeholder="请选择资源类型"
                            clearable
                            class="w200"
                        >
                            <el-option
                                v-for="item in ResourceTypes"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="">
                        <el-select
                            v-model="searchForm.resourceTag"
                            placeholder="请选择资源标签"
                            clearable
                            class="w200"
                        >
                            <el-option
                                v-for="item in resourceTags"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="handleAdd">上传资源</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <div class="table-container">
                <el-table :data="tableData" style="width: 100%" v-loading="loading">
                    <el-table-column prop="resourceName" label="资源名称" />

                    <el-table-column label="封面">
                        <template #default="scope">
                            <el-image
                                v-if="scope.row.coverUrl"
                                :src="scope.row.coverUrl"
                                :preview-src-list="[scope.row.coverUrl]"
                                style="width: 60px; height: 40px"
                                fit="cover"
                                preview-teleported
                            />
                            <span v-else class="no-cover">暂无封面</span>
                        </template>
                    </el-table-column>

                    <el-table-column prop="resourceType" label="类型" >
                        <template #default="scope">
                            {{ ResourceTypesMap[scope.row.resourceType] }}
                        </template>
                    </el-table-column>

                    <el-table-column prop="resourceTag" label="标签" >
                        <template #default="scope">
                            {{ resourceTagsMap[scope.row.resourceTag] }}
                        </template>
                    </el-table-column>

                    <el-table-column prop="uploader" label="上传人" />

                    <el-table-column prop="uploadTime" label="上传时间"  />

                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="scope">
                            <el-button
                                type="primary"
                                size="small"
                                @click="handleEdit(scope.row)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                type="danger"
                                size="small"
                                @click="handleDelete(scope.row)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.currentPage"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { ResourceTypes, ResourceTypesMap, resourceTags, resourceTagsMap } from '@/utils/static-tools'

const router = useRouter()

const searchForm = ref({
  resourceName: '',
  resourceType: '',
  resourceTag: ''
})

const loading = ref(false)

const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const tableData = ref([
{
    id: 1,
    resourceName: '高等数学教学课件',
    coverUrl: 'https://via.placeholder.com/300x200',
    resourceType: 1,
    resourceTag: 1,
    uploader: '张老师',
    uploadTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    resourceName: '线性代数微课视频',
    coverUrl: 'https://via.placeholder.com/300x200',
    resourceType: 2,
    resourceTag: 1,
    uploader: '李老师',
    uploadTime: '2024-01-14 14:20:00'
  },
  {
    id: 3,
    resourceName: '教研会议记录',
    coverUrl: '',
    resourceType: 3,
    resourceTag: 3,
    uploader: '王老师',
    uploadTime: '2024-01-13 09:15:00'
  },
  {
    id: 4,
    resourceName: '优秀教学设计案例',
    coverUrl: 'https://via.placeholder.com/300x200',
    resourceType: 5,
    resourceTag: 2,
    uploader: '赵老师',
    uploadTime: '2024-01-12 16:45:00'
  }
])

const handleSearch = () => {
  loading.value = true
}


const handleAdd = () => {
  router.push('/jiaoyanshi/resources/add')
}

const handleEdit = (row) => {
  router.push({
    path: '/jiaoyanshi/resources/add',
    query: { id: row.id }
  })
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除资源"${row.resourceName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
  })
}
const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  pagination.value.currentPage = 1
  loadData()
}

const handleCurrentChange = (val) => {
  pagination.value.currentPage = val
  loadData()
}

// const loadData = () => {
//   loading.value = true
// }

// 初始化数据
onMounted(() => {
//   loadData()
});
</script>

<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}

.search-form {
  background: #fff;
  padding: 20px;
  margin: 20px 0;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .el-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .no-cover {
    color: #999;
    font-size: 12px;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

.el-table {
  .el-button {
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }
  }
}
.w200 {
    width: 200px;
}
</style>